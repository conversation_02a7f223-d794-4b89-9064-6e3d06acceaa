{"version": "2025.0", "waypoints": [{"anchor": {"x": 3.98, "y": 5.24}, "prevControl": null, "nextControl": {"x": 3.316855850468241, "y": 5.760793783082248}, "isLocked": false, "linkedName": null}, {"anchor": {"x": 1.722, "y": 7.129}, "prevControl": {"x": 2.338563708774429, "y": 6.649638232899023}, "nextControl": null, "isLocked": false, "linkedName": null}], "rotationTargets": [{"waypointRelativePos": 0.3291032261499149, "rotationDegrees": -136.13455862983048}], "constraintZones": [{"name": "Constraints Zone", "minWaypointRelativePos": 0.5509790681971605, "maxWaypointRelativePos": 1.0, "constraints": {"maxVelocity": 1.5, "maxAcceleration": 1.5, "maxAngularVelocity": 540.0, "maxAngularAcceleration": 720.0, "nominalVoltage": 12.0, "unlimited": false}}], "pointTowardsZones": [], "eventMarkers": [{"name": "Intake", "waypointRelativePos": 0.11073598919649108, "endWaypointRelativePos": null, "command": {"type": "named", "data": {"name": "IntakeCoral"}}}], "globalConstraints": {"maxVelocity": 3.5, "maxAcceleration": 4.0, "maxAngularVelocity": 540.0, "maxAngularAcceleration": 720.0, "nominalVoltage": 12.0, "unlimited": false}, "goalEndState": {"velocity": 0, "rotation": -142.62124693823438}, "reversed": false, "folder": "Ground LSide", "idealStartingState": {"velocity": 0, "rotation": -150.82019924107797}, "useDefaultConstraints": false}