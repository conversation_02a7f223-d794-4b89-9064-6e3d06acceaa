{"version": "2025.0", "waypoints": [{"anchor": {"x": 5.0, "y": 5.24}, "prevControl": null, "nextControl": {"x": 4.856164772727273, "y": 5.775014204545455}, "isLocked": false, "linkedName": null}, {"anchor": {"x": 3.6396306818181814, "y": 6.223735795454545}, "prevControl": {"x": 3.9487500000000004, "y": 6.383281249999999}, "nextControl": {"x": 3.061864563544763, "y": 5.925533927958591}, "isLocked": false, "linkedName": null}, {"anchor": {"x": 1.722, "y": 7.129}, "prevControl": {"x": 2.562698863636364, "y": 6.044247159090909}, "nextControl": null, "isLocked": false, "linkedName": null}], "rotationTargets": [{"waypointRelativePos": 1.25, "rotationDegrees": -145.0}], "constraintZones": [{"name": "Constraints Zone", "minWaypointRelativePos": 1.6961512491559774, "maxWaypointRelativePos": 2.0, "constraints": {"maxVelocity": 1.5, "maxAcceleration": 1.5, "maxAngularVelocity": 540.0, "maxAngularAcceleration": 720.0, "nominalVoltage": 12.0, "unlimited": false}}], "pointTowardsZones": [], "eventMarkers": [{"name": "Intake", "waypointRelativePos": 0.55, "endWaypointRelativePos": null, "command": {"type": "named", "data": {"name": "IntakeCoral"}}}], "globalConstraints": {"maxVelocity": 3.5, "maxAcceleration": 4.0, "maxAngularVelocity": 540.0, "maxAngularAcceleration": 720.0, "nominalVoltage": 12.0, "unlimited": false}, "goalEndState": {"velocity": 0, "rotation": -144.0}, "reversed": false, "folder": "Ground LSide", "idealStartingState": {"velocity": 0.0, "rotation": 150.0}, "useDefaultConstraints": false}