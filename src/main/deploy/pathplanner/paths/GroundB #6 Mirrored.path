{"version": "2025.0", "waypoints": [{"anchor": {"x": 3.98, "y": 2.81}, "prevControl": null, "nextControl": {"x": 3.661208032083275, "y": 2.7146988994001573}, "isLocked": false, "linkedName": null}, {"anchor": {"x": 1.722, "y": 0.921}, "prevControl": {"x": 1.2995983363740153, "y": 0.5281644353113872}, "nextControl": null, "isLocked": false, "linkedName": null}], "rotationTargets": [{"waypointRelativePos": 0.3291032261499149, "rotationDegrees": -32.65592295916657}], "constraintZones": [{"name": "Constraints Zone", "minWaypointRelativePos": 0.5509790681971605, "maxWaypointRelativePos": 1.0, "constraints": {"maxVelocity": 1.5, "maxAcceleration": 1.5, "maxAngularVelocity": 540.0, "maxAngularAcceleration": 720.0, "nominalVoltage": 12.0, "unlimited": false}}], "pointTowardsZones": [], "eventMarkers": [{"name": "Intake", "waypointRelativePos": 0.11073598919649108, "endWaypointRelativePos": null, "command": {"type": "named", "data": {"name": "IntakeCoral"}}}], "globalConstraints": {"maxVelocity": 3.5, "maxAcceleration": 3.5, "maxAngularVelocity": 540.0, "maxAngularAcceleration": 720.0, "nominalVoltage": 12.0, "unlimited": false}, "goalEndState": {"velocity": 0, "rotation": -35.0}, "reversed": false, "folder": "Ground LSide", "idealStartingState": {"velocity": 0, "rotation": -29.999999999999996}, "useDefaultConstraints": false}