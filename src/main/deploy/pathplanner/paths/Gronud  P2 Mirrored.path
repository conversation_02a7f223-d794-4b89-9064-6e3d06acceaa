{"version": "2025.0", "waypoints": [{"anchor": {"x": 5.0, "y": 2.81}, "prevControl": null, "nextControl": {"x": 4.967147352430555, "y": 2.1109704137731478}, "isLocked": false, "linkedName": null}, {"anchor": {"x": 3.6396306818181814, "y": 1.826}, "prevControl": {"x": 3.976501550755575, "y": 1.7392387033253078}, "nextControl": {"x": 2.8861082175925916, "y": 2.020070167824074}, "isLocked": false, "linkedName": null}, {"anchor": {"x": 1.722, "y": 0.921}, "prevControl": {"x": 2.7759913917824064, "y": 2.1777043547453694}, "nextControl": null, "isLocked": false, "linkedName": null}], "rotationTargets": [{"waypointRelativePos": 1.25, "rotationDegrees": -39.29328640235812}], "constraintZones": [{"name": "Constraints Zone", "minWaypointRelativePos": 1.6961512491559774, "maxWaypointRelativePos": 2.0, "constraints": {"maxVelocity": 1.5, "maxAcceleration": 1.5, "maxAngularVelocity": 540.0, "maxAngularAcceleration": 720.0, "nominalVoltage": 12.0, "unlimited": false}}], "pointTowardsZones": [], "eventMarkers": [{"name": "Intake", "waypointRelativePos": 0.55, "endWaypointRelativePos": null, "command": {"type": "named", "data": {"name": "IntakeCoral"}}}], "globalConstraints": {"maxVelocity": 3.5, "maxAcceleration": 3.5, "maxAngularVelocity": 540.0, "maxAngularAcceleration": 720.0, "nominalVoltage": 12.0, "unlimited": false}, "goalEndState": {"velocity": 0, "rotation": -36.0}, "reversed": false, "folder": "Ground LSide", "idealStartingState": {"velocity": 0.0, "rotation": 29.999999999999996}, "useDefaultConstraints": false}