{"version": "2025.0", "command": {"type": "sequential", "data": {"commands": [{"type": "path", "data": {"pathName": "P  1"}}, {"type": "sequential", "data": {"commands": [{"type": "named", "data": {"name": "Drive To 11/20 Right"}}, {"type": "named", "data": {"name": "OuttakeCoral"}}, {"type": "named", "data": {"name": "Lower Elevator"}}]}}, {"type": "path", "data": {"pathName": "Gronud  P2"}}, {"type": "path", "data": {"pathName": "P 5"}}, {"type": "sequential", "data": {"commands": [{"type": "named", "data": {"name": "Drive To 6/19 Left"}}, {"type": "named", "data": {"name": "OuttakeCoral"}}, {"type": "named", "data": {"name": "Lower Elevator"}}]}}, {"type": "path", "data": {"pathName": "GroundB #6"}}, {"type": "path", "data": {"pathName": "P  3"}}, {"type": "sequential", "data": {"commands": [{"type": "named", "data": {"name": "Drive To 6/19 Right"}}, {"type": "named", "data": {"name": "OuttakeCoral"}}, {"type": "named", "data": {"name": "Lower Elevator"}}]}}, {"type": "path", "data": {"pathName": "3d intake peice"}}]}}, "resetOdom": true, "folder": null, "choreoAuto": false}