{"version": "2025.0", "command": {"type": "sequential", "data": {"commands": [{"type": "path", "data": {"pathName": "AP1"}}, {"type": "sequential", "data": {"commands": [{"type": "named", "data": {"name": "OuttakeCoral"}}, {"type": "named", "data": {"name": "Lower Elevator"}}]}}, {"type": "path", "data": {"pathName": "Station R P2"}}, {"type": "path", "data": {"pathName": "Station R P3"}}, {"type": "sequential", "data": {"commands": [{"type": "named", "data": {"name": "OuttakeCoral"}}, {"type": "named", "data": {"name": "Lower Elevator"}}]}}, {"type": "sequential", "data": {"commands": [{"type": "named", "data": {"name": "OuttakeCoral"}}, {"type": "named", "data": {"name": "Lower Elevator"}}]}}]}}, "resetOdom": true, "folder": null, "choreoAuto": false}