{"version": "2025.0", "command": {"type": "sequential", "data": {"commands": [{"type": "path", "data": {"pathName": "P  1"}}, {"type": "sequential", "data": {"commands": [{"type": "named", "data": {"name": "Drive To 11/20 Right"}}, {"type": "named", "data": {"name": "OuttakeCoral"}}, {"type": "named", "data": {"name": "Lower Elevator"}}]}}, {"type": "path", "data": {"pathName": "Gronud  P2"}}, {"type": "sequential", "data": {"commands": [{"type": "parallel", "data": {"commands": [{"type": "named", "data": {"name": "Drive To 6/19 Left"}}, {"type": "sequential", "data": {"commands": [{"type": "wait", "data": {"waitTime": 1.0}}, {"type": "named", "data": {"name": "L4"}}]}}]}}, {"type": "named", "data": {"name": "OuttakeCoral"}}, {"type": "named", "data": {"name": "Lower Elevator"}}]}}, {"type": "path", "data": {"pathName": "GroundB #6"}}, {"type": "sequential", "data": {"commands": [{"type": "parallel", "data": {"commands": [{"type": "named", "data": {"name": "Drive To 6/19 Right"}}, {"type": "sequential", "data": {"commands": [{"type": "wait", "data": {"waitTime": 1.0}}, {"type": "named", "data": {"name": "L4"}}]}}]}}, {"type": "named", "data": {"name": "OuttakeCoral"}}, {"type": "named", "data": {"name": "Lower Elevator"}}]}}]}}, "resetOdom": true, "folder": null, "choreoAuto": false}