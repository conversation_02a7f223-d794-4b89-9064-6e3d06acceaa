{"version": "2025.0", "command": {"type": "sequential", "data": {"commands": [{"type": "path", "data": {"pathName": "P  1 - Align After"}}, {"type": "sequential", "data": {"commands": [{"type": "named", "data": {"name": "Drive To 11/20 Right"}}, {"type": "named", "data": {"name": "OuttakeCoral"}}, {"type": "named", "data": {"name": "Lower Elevator"}}]}}, {"type": "path", "data": {"pathName": "Station P2"}}, {"type": "parallel", "data": {"commands": [{"type": "named", "data": {"name": "Station Intake"}}]}}, {"type": "path", "data": {"pathName": "StationB Alt Opt Left - Align After"}}, {"type": "sequential", "data": {"commands": [{"type": "named", "data": {"name": "Drive To 6/19 Left"}}, {"type": "named", "data": {"name": "OuttakeCoral"}}, {"type": "named", "data": {"name": "Lower Elevator"}}]}}, {"type": "path", "data": {"pathName": " Station P4"}}, {"type": "parallel", "data": {"commands": [{"type": "named", "data": {"name": "Station Intake"}}]}}, {"type": "path", "data": {"pathName": " StationB Alt Opt - Align After"}}, {"type": "sequential", "data": {"commands": [{"type": "named", "data": {"name": "Drive To 6/19 Right"}}, {"type": "named", "data": {"name": "OuttakeCoral"}}, {"type": "named", "data": {"name": "Lower Elevator"}}]}}]}}, "resetOdom": true, "folder": null, "choreoAuto": false}