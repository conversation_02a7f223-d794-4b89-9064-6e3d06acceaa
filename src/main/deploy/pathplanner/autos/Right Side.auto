{"version": "2025.0", "command": {"type": "sequential", "data": {"commands": [{"type": "path", "data": {"pathName": "P  1 Mirrored"}}, {"type": "sequential", "data": {"commands": [{"type": "named", "data": {"name": "Drive To 9/22 Left"}}, {"type": "named", "data": {"name": "OuttakeCoral"}}, {"type": "named", "data": {"name": "Lower Elevator"}}]}}, {"type": "path", "data": {"pathName": "Gronud  P2 Mirrored"}}, {"type": "path", "data": {"pathName": "P 5 Mirrored"}}, {"type": "sequential", "data": {"commands": [{"type": "named", "data": {"name": "Drive To 8/17 Right"}}, {"type": "named", "data": {"name": "OuttakeCoral"}}, {"type": "named", "data": {"name": "Lower Elevator"}}]}}, {"type": "path", "data": {"pathName": "GroundB #6 Mirrored"}}, {"type": "path", "data": {"pathName": "P  3 Mirrored"}}, {"type": "sequential", "data": {"commands": [{"type": "named", "data": {"name": "Drive To 8/17 Left"}}, {"type": "named", "data": {"name": "OuttakeCoral"}}, {"type": "named", "data": {"name": "Lower Elevator"}}]}}]}}, "resetOdom": true, "folder": null, "choreoAuto": false}