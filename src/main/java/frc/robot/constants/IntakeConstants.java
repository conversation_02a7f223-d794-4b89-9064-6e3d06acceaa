package frc.robot.constants;

/**
 * Storage class for intake constants
 */
public class IntakeConstants {
    public static final double PIVOT_GEAR_RATIO = 75;
    public static final double MOMENT_OFiNERTIA = 0.600984136;
    public static final double CENTER_OF_MASS_DIST = 0.265;
    public static final double MASS = 6.45688739;
    public static final double ARM_LENGTH = CENTER_OF_MASS_DIST*2;
    public static final double DETECT_CORAL_DIST = 0.49;
    public static final double INTAKE_MOTOR_POWER = 1;
    public static final double INTAKE_SETPOINT = -30.41;
    public static final double STOW_SETPOINT = 90;
    public static final double STATION_SETPOINT = 70;
    public static final double INTAKE_SAFE_POINT = 30;

    public static final double ALGAE_SETPOINT = 25;
    public static final double ALGAE_INTAKE_POWER = -0.5;
    public static final double ALGAE_OUTTAKE_POWER = 0.5;
   
}
